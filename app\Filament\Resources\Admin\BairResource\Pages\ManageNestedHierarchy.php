<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Filament\Resources\Admin\BairResource;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ManageNestedHierarchy extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = BairResource::class;

    protected static string $view = 'filament.resources.admin.bair-resource.pages.manage-nested-hierarchy';

    protected static ?string $title = 'Иерархийн бүтэц удирдах';

    protected static ?string $navigationLabel = 'Бүтэц удирдах';

    public ?array $data = [];

    public function mount(): void
    {
        $bair = $this->getRecord();

        // Load existing hierarchy data
        $korpuses = $bair->korpuses()->with(['orcs.davhars.toots'])->orderBy('order')->get();

        $this->form->fill([
            'korpuses' => $korpuses->map(function ($korpus) {
                return [
                    'id' => $korpus->id,
                    'name' => $korpus->name,
                    'order' => $korpus->order,
                    'orcs' => $korpus->orcs->map(function ($orc) {
                        return [
                            'id' => $orc->id,
                            'number' => $orc->number,
                            'davhars' => $orc->davhars->map(function ($davhar) {
                                return [
                                    'id' => $davhar->id,
                                    'number' => $davhar->number,
                                    'order' => $davhar->order,
                                    'toots' => $davhar->toots->map(function ($toot) {
                                        return [
                                            'id' => $toot->id,
                                            'number' => $toot->number,
                                        ];
                                    })->toArray(),
                                ];
                            })->toArray(),
                        ];
                    })->toArray(),
                ];
            })->toArray(),
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Иерархийн бүтэц')
                    ->description('Байр → Блок → Орц → Давхар → Тоот')
                    ->schema([
                        Forms\Components\Repeater::make('korpuses')
                            ->label('Блокууд')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('name')
                                            ->label('Блокийн нэр')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\TextInput::make('order')
                                            ->label('Дараалал')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1),
                                    ]),
                                Forms\Components\Repeater::make('orcs')
                                    ->label('Орцууд')
                                    ->schema([
                                        Forms\Components\TextInput::make('number')
                                            ->label('Орцны дугаар')
                                            ->required()
                                            ->maxLength(255),
                                        Forms\Components\Repeater::make('davhars')
                                            ->label('Давхарууд')
                                            ->schema([
                                                Forms\Components\Grid::make(2)
                                                    ->schema([
                                                        Forms\Components\TextInput::make('number')
                                                            ->label('Давхарын дугаар')
                                                            ->required()
                                                            ->maxLength(255),
                                                        Forms\Components\TextInput::make('order')
                                                            ->label('Дараалал')
                                                            ->numeric()
                                                            ->required()
                                                            ->minValue(1),
                                                    ]),
                                                Forms\Components\Repeater::make('toots')
                                                    ->label('Тоотууд')
                                                    ->schema([
                                                        Forms\Components\TextInput::make('number')
                                                            ->label('Тоотын дугаар')
                                                            ->numeric()
                                                            ->required(),
                                                    ])
                                                    ->columns(3)
                                                    ->collapsible()
                                                    ->collapsed()
                                                    ->itemLabel(fn (array $state): ?string => $state['number'] ?? null)
                                                    ->addActionLabel('Тоот нэмэх')
                                                    ->deleteAction(
                                                        fn (Forms\Components\Actions\Action $action) => $action
                                                            ->requiresConfirmation()
                                                    ),
                                            ])
                                            ->columns(1)
                                            ->collapsible()
                                            ->collapsed()
                                            ->itemLabel(fn (array $state): ?string =>
                                                isset($state['number']) ? "Давхар {$state['number']}" : null
                                            )
                                            ->addActionLabel('Давхар нэмэх')
                                            ->deleteAction(
                                                fn (Forms\Components\Actions\Action $action) => $action
                                                    ->requiresConfirmation()
                                            ),
                                    ])
                                    ->columns(1)
                                    ->collapsible()
                                    ->collapsed()
                                    ->itemLabel(fn (array $state): ?string =>
                                        isset($state['number']) ? "Орц {$state['number']}" : null
                                    )
                                    ->addActionLabel('Орц нэмэх')
                                    ->deleteAction(
                                        fn (Forms\Components\Actions\Action $action) => $action
                                            ->requiresConfirmation()
                                    ),
                            ])
                            ->columns(1)
                            ->collapsible()
                            ->itemLabel(fn (array $state): ?string =>
                                isset($state['name']) ? "Блок {$state['name']}" : null
                            )
                            ->addActionLabel('Блок нэмэх')
                            ->deleteAction(
                                fn (Forms\Components\Actions\Action $action) => $action
                                    ->requiresConfirmation()
                            )
                            ->reorderable()
                            ->orderColumn('order'),
                    ]),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();
        $bair = $this->getRecord();

        try {
            DB::transaction(function () use ($data, $bair) {
                // Process korpuses
                $existingKorpusIds = [];

                foreach ($data['korpuses'] ?? [] as $korpusData) {
                    if (isset($korpusData['id'])) {
                        // Update existing korpus
                        $korpus = Korpus::find($korpusData['id']);
                        $korpus->update([
                            'name' => $korpusData['name'],
                            'order' => $korpusData['order'],
                        ]);
                        $existingKorpusIds[] = $korpus->id;
                    } else {
                        // Create new korpus
                        $korpus = Korpus::create([
                            'bair_id' => $bair->id,
                            'name' => $korpusData['name'],
                            'order' => $korpusData['order'],
                        ]);
                        $existingKorpusIds[] = $korpus->id;
                    }

                    // Process orcs for this korpus
                    $this->processOrcs($korpus, $korpusData['orcs'] ?? []);
                }

                // Delete removed korpuses
                $bair->korpuses()->whereNotIn('id', $existingKorpusIds)->delete();
            });

            Notification::make()
                ->title('Амжилттай хадгалагдлаа')
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Алдаа гарлаа')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    private function processOrcs(Korpus $korpus, array $orcsData): void
    {
        $existingOrcIds = [];

        foreach ($orcsData as $orcData) {
            if (isset($orcData['id'])) {
                // Update existing orc
                $orc = Orc::find($orcData['id']);
                $orc->update(['number' => $orcData['number']]);
                $existingOrcIds[] = $orc->id;
            } else {
                // Create new orc
                $orc = Orc::create([
                    'korpus_id' => $korpus->id,
                    'number' => $orcData['number'],
                ]);
                $existingOrcIds[] = $orc->id;
            }

            // Process davhars for this orc
            $this->processDavhars($orc, $orcData['davhars'] ?? []);
        }

        // Delete removed orcs
        $korpus->orcs()->whereNotIn('id', $existingOrcIds)->delete();
    }

    private function processDavhars(Orc $orc, array $davharsData): void
    {
        $existingDavharIds = [];

        foreach ($davharsData as $davharData) {
            if (isset($davharData['id'])) {
                // Update existing davhar
                $davhar = Davhar::find($davharData['id']);
                $davhar->update([
                    'number' => $davharData['number'],
                    'order' => $davharData['order'],
                ]);
                $existingDavharIds[] = $davhar->id;
            } else {
                // Create new davhar
                $davhar = Davhar::create([
                    'orc_id' => $orc->id,
                    'number' => $davharData['number'],
                    'order' => $davharData['order'],
                ]);
                $existingDavharIds[] = $davhar->id;
            }

            // Process toots for this davhar
            $this->processToots($davhar, $davharData['toots'] ?? []);
        }

        // Delete removed davhars
        $orc->davhars()->whereNotIn('id', $existingDavharIds)->delete();
    }

    private function processToots(Davhar $davhar, array $tootsData): void
    {
        $existingTootIds = [];

        foreach ($tootsData as $tootData) {
            if (isset($tootData['id'])) {
                // Update existing toot
                $toot = Toot::find($tootData['id']);
                $toot->update(['number' => $tootData['number']]);
                $existingTootIds[] = $toot->id;
            } else {
                // Create new toot
                $toot = Toot::create([
                    'davhar_id' => $davhar->id,
                    'korpus_id' => $davhar->orc->korpus_id,
                    'number' => $tootData['number'],
                ]);
                $existingTootIds[] = $toot->id;
            }
        }

        // Delete removed toots
        $davhar->toots()->whereNotIn('id', $existingTootIds)->delete();
    }

    protected function getFormActions(): array
    {
        return [
            \Filament\Actions\Action::make('save')
                ->label('Хадгалах')
                ->submit('save')
                ->keyBindings(['mod+s']),
            \Filament\Actions\Action::make('cancel')
                ->label('Цуцлах')
                ->url(fn (): string => route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id]))
                ->color('gray'),
        ];
    }

    public function getRecord(): Bair
    {
        return Bair::findOrFail(request()->route('record'));
    }
}
