<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Bair
 *
 * @mixin IdeHelperBair
 * @property int $id
 * @property string $name
 * @property int|null $sukh_id
 * @property int|null $aimag_id
 * @property int|null $soum_id
 * @property int|null $bag_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Aimag|null $aimag
 * @property-read \App\Models\Bag|null $bag
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Korpus> $korpuses
 * @property-read int|null $korpuses_count
 * @property-read \App\Models\Soum|null $soum
 * @property-read \App\Models\Sukh|null $sukh
 * @method static \Illuminate\Database\Eloquent\Builder|Bair newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bair newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Bair query()
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereAimagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereBagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereSoumId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereSukhId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Bair whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Bair extends Model
{
    use HasFactory;

    const ID                  = 'id';
    const NAME                = 'name';
    const SUKH_ID             = 'sukh_id';
    const AIMAG_ID            = 'aimag_id';
    const SOUM_ID             = 'soum_id';
    const BAG_ID              = 'bag_id';

    const RELATION_SUKH       = 'sukh';
    const RELATION_AIMAG      = 'aimag';
    const RELATION_SOUM       = 'soum';
    const RELATION_BAG        = 'bag';
    const RELATION_KORPUS     = 'korpuses';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::SUKH_ID,
        self::AIMAG_ID,
        self::SOUM_ID,
        self::BAG_ID,
    ];

    public function sukh(): BelongsTo
    {
        return $this->belongsTo(Sukh::class);
    }

    public function aimag(): BelongsTo
    {
        return $this->belongsTo(Aimag::class);
    }

    public function soum(): BelongsTo
    {
        return $this->belongsTo(Soum::class);
    }

    public function bag(): BelongsTo
    {
        return $this->belongsTo(Bag::class);
    }

    public function korpuses(): HasMany
    {
        return $this->hasMany(Korpus::class)->orderBy('order', 'asc');
    }

    public function orcs(): HasManyThrough
    {
        return $this->hasManyThrough(Orc::class, Korpus::class);
    }

    public function toots(): HasManyThrough
    {
        return $this->hasManyThrough(Toot::class, Korpus::class);
    }
}
