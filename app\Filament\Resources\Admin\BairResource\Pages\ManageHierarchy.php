<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Filament\Resources\Admin\BairResource;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\Toot;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;

class ManageHierarchy extends Page implements Tables\Contracts\HasTable, Forms\Contracts\HasForms, Infolists\Contracts\HasInfolists
{
    use InteractsWithTable;
    use InteractsWithForms;
    use InteractsWithInfolists;

    protected static string $resource = BairResource::class;

    protected static string $view = 'filament.resources.admin.bair-resource.pages.manage-hierarchy';

    protected static ?string $title = 'Иерархи удирдах';

    protected static ?string $navigationLabel = 'Иерархи удирдах';

    protected static ?int $navigationSort = 10;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Иерархийн бүтэц')
                    ->description('Байр → Блок → Орц → Давхар → Тоот')
                    ->schema([
                        Forms\Components\Placeholder::make('hierarchy_info')
                            ->label('')
                            ->content(function () {
                                $bair = $this->getRecord();
                                $korpusCount = $bair->korpuses()->count();
                                $orcCount = $bair->orcs()->count();
                                $davharCount = Davhar::whereHas('orc.korpus', function ($query) use ($bair) {
                                    $query->where('bair_id', $bair->id);
                                })->count();
                                $tootCount = $bair->toots()->count();

                                return view('filament.components.hierarchy-stats', [
                                    'bair' => $bair,
                                    'korpusCount' => $korpusCount,
                                    'orcCount' => $orcCount,
                                    'davharCount' => $davharCount,
                                    'tootCount' => $tootCount,
                                ]);
                            }),
                    ]),
            ])
            ->statePath('data');
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('korpus.name')
                    ->label('Блок')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('number')
                    ->label('Орцны дугаар')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('davhars_count')
                    ->counts('davhars')
                    ->label('Давхар тоо')
                    ->sortable(),
                Tables\Columns\TextColumn::make('toots_count')
                    ->counts('toots')
                    ->label('Тоот тоо')
                    ->sortable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('CVSecurity код')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('korpus_id')
                    ->label('Блок')
                    ->options(function () {
                        $bair = $this->getRecord();
                        return Korpus::where('bair_id', $bair->id)
                            ->orderBy('order')
                            ->pluck('name', 'id');
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('manage_davhars')
                    ->label('Давхар удирдах')
                    ->icon('heroicon-o-building-office')
                    ->url(fn (Orc $record): string => route('filament.admin.resources.orcs.edit', $record))
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('manage_toots')
                    ->label('Тоот удирдах')
                    ->icon('heroicon-o-home')
                    ->action(function (Orc $record) {
                        // This will be implemented as a modal or redirect
                        $this->redirect(route('filament.admin.resources.orcs.edit', $record));
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateHeading('Орц байхгүй байна')
            ->emptyStateDescription('Эхлээд блок нэмээд дараа нь орц нэмнэ үү.')
            ->emptyStateActions([
                Tables\Actions\Action::make('create_korpus')
                    ->label('Блок нэмэх')
                    ->icon('heroicon-o-plus')
                    ->url(fn (): string => route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id])),
            ]);
    }

    protected function getTableQuery(): Builder
    {
        $bair = $this->getRecord();
        
        return Orc::query()
            ->with(['korpus', 'davhars', 'toots'])
            ->whereHas('korpus', function ($query) use ($bair) {
                $query->where('bair_id', $bair->id);
            })
            ->orderBy('korpus_id')
            ->orderBy('number');
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->getRecord())
            ->schema([
                Infolists\Components\Section::make('Байрны мэдээлэл')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Нэр'),
                        Infolists\Components\TextEntry::make('sukh.name')
                            ->label('СӨХ'),
                        Infolists\Components\TextEntry::make('aimag.name')
                            ->label('Аймаг/Хот'),
                        Infolists\Components\TextEntry::make('soum.name')
                            ->label('Сум/Дүүрэг'),
                        Infolists\Components\TextEntry::make('bag.name')
                            ->label('Баг/Хороо'),
                    ])
                    ->columns(2),
            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('back_to_bair')
                ->label('Байр руу буцах')
                ->icon('heroicon-o-arrow-left')
                ->url(fn (): string => route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id])),
        ];
    }

    public function getRecord(): Bair
    {
        return Bair::findOrFail(request()->route('record'));
    }
}
