<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section with Bair Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                    {{ $this->getRecord()->name }} - Иерархийн удирдлага
                </h2>
                <div class="flex space-x-2">
                    <x-filament::button
                        tag="a"
                        :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id])"
                        icon="heroicon-o-pencil"
                        size="sm"
                    >
                        Засах
                    </x-filament::button>
                </div>
            </div>
            
            {{ $this->infolist }}
        </div>

        <!-- Hierarchy Stats -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            {{ $this->form }}
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id]) . '#korpuses'"
                icon="heroicon-o-building-office-2"
                color="primary"
                class="justify-center"
            >
                Блок удирдах
            </x-filament::button>
            
            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id]) . '#orcs'"
                icon="heroicon-o-building-office"
                color="success"
                class="justify-center"
            >
                Орц удирдах
            </x-filament::button>
            
            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id]) . '#davhars'"
                icon="heroicon-o-building-storefront"
                color="warning"
                class="justify-center"
            >
                Давхар удирдах
            </x-filament::button>
            
            <x-filament::button
                tag="a"
                :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id]) . '#toots'"
                icon="heroicon-o-home"
                color="danger"
                class="justify-center"
            >
                Тоот удирдах
            </x-filament::button>
        </div>

        <!-- Orcs Table with Hierarchy View -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    Орцууд болон тэдгээрийн давхар, тоот
                </h3>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    Байрын бүх орцуудыг харж, тэдгээрийн давхар, тоотуудыг удирдана уу.
                </p>
            </div>
            
            {{ $this->table }}
        </div>

        <!-- CVSecurity Integration Status -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                CVSecurity интеграци
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-green-800 dark:text-green-200 text-sm font-medium">
                            Автомат синхрончлол идэвхтэй
                        </span>
                    </div>
                    <p class="mt-1 text-green-700 dark:text-green-300 text-xs">
                        CRUD үйлдлүүд автоматаар CVSecurity-тэй синхрончлогддог.
                    </p>
                </div>
                
                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-blue-800 dark:text-blue-200 text-sm font-medium">
                            Тоотны дугаарлалт дэмжигдсэн
                        </span>
                    </div>
                    <p class="mt-1 text-blue-700 dark:text-blue-300 text-xs">
                        Type 1/2/3 дугаарлалтын систем ашиглаж болно.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
