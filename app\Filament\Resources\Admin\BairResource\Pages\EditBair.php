<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Filament\Resources\Admin\BairResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBair extends EditRecord
{
    protected static string $resource = BairResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('manage_hierarchy')
                ->label('Иерархи удирдах')
                ->icon('heroicon-o-building-office-2')
                ->color('primary')
                ->url(fn (): string => route('filament.admin.resources.bairs.manage-hierarchy', ['record' => $this->record])),
            Actions\Action::make('manage_nested_hierarchy')
                ->label('Бүтэц удирдах')
                ->icon('heroicon-o-squares-plus')
                ->color('success')
                ->url(fn (): string => route('filament.admin.resources.bairs.manage-nested-hierarchy', ['record' => $this->record])),
            Actions\DeleteAction::make(),
        ];
    }
}
