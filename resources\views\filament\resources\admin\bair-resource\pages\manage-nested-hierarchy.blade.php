<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        {{ $this->getRecord()->name }} - Иерархийн бүтэц удирдах
                    </h2>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Байрын бүх иерархийг нэг дор удирдаж, CVSecurity-тэй автомат синхрончлох
                    </p>
                </div>
                <div class="flex space-x-2">
                    <x-filament::button
                        tag="a"
                        :href="route('filament.admin.resources.bairs.manage-hierarchy', ['record' => $this->getRecord()->id])"
                        icon="heroicon-o-table-cells"
                        size="sm"
                        color="gray"
                    >
                        Хүснэгт харах
                    </x-filament::button>
                    <x-filament::button
                        tag="a"
                        :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id])"
                        icon="heroicon-o-pencil"
                        size="sm"
                    >
                        Байр засах
                    </x-filament::button>
                </div>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Иерархийн удирдлагын заавар
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Блок → Орц → Давхар → Тоот гэсэн дарааллаар иерархи бүтээнэ үү</li>
                            <li>Бүх өөрчлөлт CVSecurity системтэй автоматаар синхрончлогдоно</li>
                            <li>Тоотны дугаарлалт Types 1/2/3 дэмжигдэнэ</li>
                            <li>Ctrl+S дарж хадгална уу</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Hierarchy Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6">
                <form wire:submit="save">
                    {{ $this->form }}
                    
                    <div class="mt-6 flex justify-end space-x-3">
                        <x-filament::button
                            type="button"
                            color="gray"
                            tag="a"
                            :href="route('filament.admin.resources.bairs.edit', ['record' => $this->getRecord()->id])"
                        >
                            Цуцлах
                        </x-filament::button>
                        
                        <x-filament::button
                            type="submit"
                            icon="heroicon-o-check"
                        >
                            Хадгалах
                        </x-filament::button>
                    </div>
                </form>
            </div>
        </div>

        <!-- CVSecurity Integration Info -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                CVSecurity интеграци
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-green-800 dark:text-green-200 text-sm font-medium">
                            Автомат синхрончлол
                        </span>
                    </div>
                    <p class="mt-1 text-green-700 dark:text-green-300 text-xs">
                        Бүх CRUD үйлдлүүд автоматаар CVSecurity-тэй синхрончлогдоно.
                    </p>
                </div>
                
                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-blue-800 dark:text-blue-200 text-sm font-medium">
                            Тоотны дугаарлалт
                        </span>
                    </div>
                    <p class="mt-1 text-blue-700 dark:text-blue-300 text-xs">
                        Type 1/2/3 дугаарлалтын систем дэмжигдэнэ.
                    </p>
                </div>
                
                <div class="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-purple-800 dark:text-purple-200 text-sm font-medium">
                            Алдааны удирдлага
                        </span>
                    </div>
                    <p class="mt-1 text-purple-700 dark:text-purple-300 text-xs">
                        Сервисийн алдаа гарсан тохиолдолд graceful handling.
                    </p>
                </div>
            </div>
        </div>

        <!-- Keyboard Shortcuts -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
            <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2">Гарын товчлол:</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600 dark:text-gray-400">
                <div><kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded">Ctrl + S</kbd> Хадгалах</div>
                <div><kbd class="px-2 py-1 bg-gray-200 dark:bg-gray-700 rounded">Esc</kbd> Цуцлах</div>
            </div>
        </div>
    </div>

    <style>
        /* Custom styles for nested repeaters */
        .fi-fo-repeater-item {
            border-left: 3px solid transparent;
        }
        
        .fi-fo-repeater-item[data-level="1"] {
            border-left-color: #3b82f6; /* Blue for Korpus */
        }
        
        .fi-fo-repeater-item[data-level="2"] {
            border-left-color: #10b981; /* Green for Orc */
        }
        
        .fi-fo-repeater-item[data-level="3"] {
            border-left-color: #f59e0b; /* Yellow for Davhar */
        }
        
        .fi-fo-repeater-item[data-level="4"] {
            border-left-color: #8b5cf6; /* Purple for Toot */
        }
    </style>
</x-filament-panels::page>
