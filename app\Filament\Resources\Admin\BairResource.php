<?php

namespace App\Filament\Resources\Admin;

use App\Models\Constant\ConstData;
use App\Models\Bair;
use App\Models\Aimag;
use App\Services\InfoService;
use App\Services\UserInfoService;
use App\Filament\Resources\Admin\BairResource\Pages;
use App\Filament\Resources\Admin\BairResource\RelationManagers;
use Illuminate\Database\Eloquent\Builder;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BairResource extends Resource
{
    protected static ?string $model = Bair::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?string $navigationLabel = 'Байр';
    protected static ?string $modelLabel = 'байр';
    protected static ?string $pluralModelLabel = 'Байр';
    protected static ?int $navigationSort = 1;
    protected static ?string $slug = 'bairs';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->label('Нэр')
                        ->maxValue(50)
                        ->required(),

                    Forms\Components\Select::make('aimag_id')
                        ->label('Аймаг/Хот')
                        ->options(Aimag::all()->pluck(ConstData::NAME, ConstData::ID))
                        ->default(function (callable $get) {
                            $service = resolve(UserInfoService::class);
                            $sukh = $service->getAUSukh();
                            return $sukh->aimag ? $sukh->aimag->id : null;
                        })
                        ->searchable(),

                    Forms\Components\Select::make('soum_id')
                        ->label('Сум/Дүүрэг')
                        ->options(
                            function (callable $get) {
                                $service = resolve(InfoService::class);
                                return $service->getSoums($get('aimag_id'));
                            }
                        )
                        ->default(function (callable $get) {
                            $service = resolve(UserInfoService::class);
                            $sukh = $service->getAUSukh();
                            return $sukh->soum ? $sukh->soum->id : null;
                        })
                        ->placeholder('Сонгох')
                        ->searchable(),

                    Forms\Components\Select::make('bag_id')
                        ->label('Баг/Хороо')
                        ->options(
                            function (callable $get) {
                                $service = resolve(InfoService::class);
                                return $service->getBags($get('aimag_id'), $get('soum_id'));
                            }
                        )
                        ->default(function (callable $get) {
                            $service = resolve(UserInfoService::class);
                            $sukh = $service->getAUSukh();
                            return $sukh->bag ? $sukh->bag->id : null;
                        })
                        ->placeholder('Сонгох')
                        ->searchable(),
                ])
                ->columns(2)
                ->columnSpan(['lg' => fn (?Bair $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (Bair $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (Bair $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?Bair $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Блокууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('aimag.name')->label('Аймаг/хот')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('soum.name')->label('Сум/дүүрэг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bag.name')->label('Баг/хороо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('manage_hierarchy')
                        ->label('Иерархи удирдах')
                        ->icon('heroicon-o-building-office-2')
                        ->color('primary')
                        ->url(fn (Bair $record): string => route('filament.admin.resources.bairs.manage-hierarchy', $record)),
                    Tables\Actions\Action::make('manage_nested_hierarchy')
                        ->label('Бүтэц удирдах')
                        ->icon('heroicon-o-squares-plus')
                        ->color('success')
                        ->url(fn (Bair $record): string => route('filament.admin.resources.bairs.manage-nested-hierarchy', $record)),
                ])
                    ->label('Удирдлага')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->size('sm')
                    ->button(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\KorpusesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBairs::route('/'),
            'create' => Pages\CreateBair::route('/create'),
            'edit' => Pages\EditBair::route('/{record}/edit'),
            'manage-hierarchy' => Pages\ManageHierarchy::route('/{record}/hierarchy'),
            'manage-nested-hierarchy' => Pages\ManageNestedHierarchy::route('/{record}/nested-hierarchy'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh    = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
